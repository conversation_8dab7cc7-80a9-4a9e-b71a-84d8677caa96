# 前端静态文件路径修复指南

## 🐛 问题描述

在部署过程中遇到以下错误：
```
Error: ENOENT: no such file or directory, stat '/home/<USER>/network-debug-helper/server/public/index.html'
```

## 🔍 问题原因

1. **路径配置错误**: 静态服务器在 `server` 目录下运行，但尝试访问 `server/public/index.html`
2. **工作目录问题**: 实际的静态文件在项目根目录的 `public` 文件夹中
3. **相对路径解析**: PM2 启动时的工作目录与预期不符

## ✅ 解决方案

### 方案一：使用启动脚本（已实施）

创建了 `start-frontend.js` 启动脚本来确保正确的工作目录：

```javascript
// start-frontend.js
const child = spawn('node', [staticServerPath], {
    cwd: projectRoot,  // 设置工作目录为项目根目录
    stdio: 'inherit',
    env: {
        ...process.env,
        STATIC_DIR: './public'  // 相对于项目根目录
    }
});
```

### 方案二：修复静态服务器路径解析

更新了 `server/static-server.js` 中的路径计算逻辑：

```javascript
// 计算静态文件目录的绝对路径
const staticPath = path.isAbsolute(STATIC_DIR) 
    ? STATIC_DIR 
    : path.resolve(process.cwd(), STATIC_DIR);
```

## 🔧 修复后的文件结构

```
network-debug-helper/
├── public/                 # 前端构建文件
│   ├── index.html
│   └── assets/
├── server/                 # 后端源码
│   ├── server.js
│   └── static-server.js
├── start-frontend.js       # 前端启动脚本
├── ecosystem.config.cjs    # PM2配置
└── package.json
```

## 🚀 部署验证

### 1. 检查文件存在
```bash
ls -la public/index.html
ls -la server/static-server.js
ls -la start-frontend.js
```

### 2. 手动测试静态服务器
```bash
# 在项目根目录下测试
node start-frontend.js
```

### 3. 检查PM2启动
```bash
pm2 start ecosystem.config.cjs
pm2 logs network-debug-helper-frontend
```

### 4. 验证路径输出
在日志中应该看到：
```
项目根目录: /home/<USER>/network-debug-helper
静态文件目录: /home/<USER>/network-debug-helper/public
🌐 前端服务器运行在 0.0.0.0:5000
```

## 🔍 故障排除步骤

### 步骤1: 检查文件结构
```bash
cd /home/<USER>/network-debug-helper
find . -name "index.html"
find . -name "static-server.js"
```

预期输出：
```
./public/index.html
./server/static-server.js
```

### 步骤2: 检查工作目录
```bash
pm2 logs network-debug-helper-frontend --lines 20
```

查找以下关键信息：
- 项目根目录路径
- 静态文件目录路径
- 是否有 "ENOENT" 错误

### 步骤3: 手动启动测试
```bash
# 停止PM2服务
pm2 stop network-debug-helper-frontend

# 手动启动测试
cd /home/<USER>/network-debug-helper
node start-frontend.js
```

### 步骤4: 检查环境变量
```bash
pm2 show network-debug-helper-frontend
```

确认环境变量：
- `STATIC_DIR`: `./public`
- `PORT`: `5000`
- `HOST`: 配置的IP地址

## 🛠️ 如果问题仍然存在

### 方法1: 重新部署
```bash
# 停止所有服务
pm2 stop all
pm2 delete all

# 重新解压部署包
rm -rf network-debug-helper
tar -xzf network-debug-helper-deploy.tar.gz
cd network-debug-helper

# 重新部署
./deploy.sh
```

### 方法2: 手动修复路径
```bash
# 编辑静态服务器配置
vim server/static-server.js

# 确保STATIC_DIR指向正确路径
const STATIC_DIR = process.env.STATIC_DIR || '../public';
```

### 方法3: 检查权限
```bash
# 检查文件权限
ls -la public/
ls -la server/
chmod +x start-frontend.js
```

### 方法4: 使用绝对路径
```bash
# 修改PM2配置使用绝对路径
vim ecosystem.config.cjs

# 在env中添加绝对路径
env: {
  STATIC_DIR: '/home/<USER>/network-debug-helper/public'
}
```

## 📊 验证修复成功

### 1. 服务状态检查
```bash
pm2 status
```

应该显示两个服务都在运行：
- `network-debug-helper` (online)
- `network-debug-helper-frontend` (online)

### 2. 端口监听检查
```bash
netstat -tuln | grep :5000
```

应该显示端口5000在监听。

### 3. HTTP访问测试
```bash
curl -I http://localhost:5000
```

应该返回 `200 OK` 状态码。

### 4. 浏览器访问测试
访问 `http://your-server-ip:5000` 应该能看到网络调试助手界面。

## 💡 预防措施

### 1. 使用启动脚本
始终使用 `start-frontend.js` 启动前端服务，确保正确的工作目录。

### 2. 绝对路径配置
在生产环境中考虑使用绝对路径配置，避免相对路径问题。

### 3. 部署前测试
在部署前使用 `npm run test:deployment` 验证部署包完整性。

### 4. 日志监控
定期检查PM2日志，及时发现路径相关问题。

## 📚 相关文档

- [部署指南](./DEPLOYMENT_GUIDE.md)
- [PM2配置修复](./PM2_FIX_GUIDE.md)
- [IP配置指南](./IP_CONFIGURATION_GUIDE.md)

通过这些修复措施，前端静态文件路径问题应该得到彻底解决！
