{"server": {"backend": {"host": "0.0.0.0", "port": 3000, "auto_detect_ip": true, "custom_ip": ""}, "frontend": {"host": "0.0.0.0", "port": 5000, "auto_detect_ip": true, "custom_ip": ""}}, "deployment": {"description": "配置说明：", "backend_config": "backend.auto_detect_ip 为 false 时，将使用 backend.custom_ip 作为后端服务器IP地址", "frontend_config": "frontend.auto_detect_ip 为 false 时，将使用 frontend.custom_ip 作为前端服务器IP地址", "note": "host 字段用于服务绑定的网络接口，通常设置为 0.0.0.0 以允许外部访问"}}