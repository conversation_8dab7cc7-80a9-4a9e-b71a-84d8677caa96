import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 5000;
const HOST = process.env.HOST || '0.0.0.0';
const STATIC_DIR = process.env.STATIC_DIR || './public';

// 计算静态文件目录的绝对路径
const staticPath = path.isAbsolute(STATIC_DIR)
    ? STATIC_DIR
    : path.resolve(process.cwd(), STATIC_DIR);

console.log(`静态文件目录: ${staticPath}`);

// 静态文件服务
app.use(express.static(staticPath));

// SPA 路由处理 - 所有路由都返回 index.html
app.get('*', (req, res) => {
    const indexPath = path.join(staticPath, 'index.html');
    console.log(`尝试访问: ${indexPath}`);
    res.sendFile(indexPath);
});

app.listen(PORT, HOST, () => {
    console.log(`🌐 前端服务器运行在 ${HOST}:${PORT}`);
    console.log(`📁 静态文件目录: ${staticPath}`);
    console.log(`🔗 访问地址: http://${HOST}:${PORT}`);
});
