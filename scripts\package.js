import fs from 'fs-extra';
import archiver from 'archiver';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

async function createDeploymentPackage() {
    console.log('🚀 开始创建部署包...');

    const deployDir = path.join(rootDir, 'deploy');
    const distDir = path.join(rootDir, 'dist');

    // 清理并创建部署目录
    await fs.remove(deployDir);
    await fs.ensureDir(deployDir);

    // 检查构建文件是否存在
    if (!await fs.pathExists(distDir)) {
        console.error('❌ 构建文件不存在，请先运行 npm run build');
        process.exit(1);
    }

    console.log('📦 复制文件到部署目录...');

    // 复制构建后的前端文件
    await fs.copy(distDir, path.join(deployDir, 'public'));

    // 复制后端文件
    await fs.copy(path.join(rootDir, 'server'), path.join(deployDir, 'server'));

    // 创建生产环境的 package.json
    const originalPackage = await fs.readJson(path.join(rootDir, 'package.json'));
    const prodPackage = {
        name: originalPackage.name,
        version: originalPackage.version,
        type: "module",
        scripts: {
            start: "node server/server.js",
            "pm2:start": "pm2 start ecosystem.config.cjs",
            "pm2:stop": "pm2 stop network-debug-helper",
            "pm2:restart": "pm2 restart network-debug-helper",
            "pm2:logs": "pm2 logs network-debug-helper"
        },
        dependencies: {
            cors: originalPackage.dependencies.cors,
            express: originalPackage.dependencies.express,
            ws: originalPackage.dependencies.ws
        }
    };

    await fs.writeJson(path.join(deployDir, 'package.json'), prodPackage, { spaces: 2 });

    // 复制配置文件
    await fs.copy(path.join(rootDir, 'ecosystem.config.cjs'), path.join(deployDir, 'ecosystem.config.cjs'));
    await fs.copy(path.join(rootDir, 'deploy.sh'), path.join(deployDir, 'deploy.sh'));
    await fs.copy(path.join(rootDir, 'config.json'), path.join(deployDir, 'config.json'));
    await fs.copy(path.join(rootDir, 'start-frontend.js'), path.join(deployDir, 'start-frontend.js'));
    await fs.copy(path.join(rootDir, 'README.md'), path.join(deployDir, 'README.md'));

    // 创建压缩包
    console.log('🗜️  创建压缩包...');
    const output = fs.createWriteStream(path.join(rootDir, 'network-debug-helper-deploy.tar.gz'));
    const archive = archiver('tar', { gzip: true });

    output.on('close', () => {
        console.log(`✅ 部署包创建完成: network-debug-helper-deploy.tar.gz (${archive.pointer()} bytes)`);
        console.log('📋 部署说明:');
        console.log('1. 将 network-debug-helper-deploy.tar.gz 上传到 Linux 服务器');
        console.log('2. 解压: tar -xzf network-debug-helper-deploy.tar.gz');
        console.log('3. 进入目录: cd network-debug-helper');
        console.log('4. 运行部署脚本: chmod +x deploy.sh && ./deploy.sh');
        console.log('5. 访问: http://your-server-ip:5000');
    });

    archive.on('error', (err) => {
        throw err;
    });

    archive.pipe(output);
    archive.directory(deployDir, 'network-debug-helper');
    archive.finalize();
}

createDeploymentPackage().catch(console.error);
