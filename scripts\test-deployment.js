import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

async function testDeploymentPackage() {
    console.log('🧪 测试部署包完整性...');

    const deployDir = path.join(rootDir, 'deploy');

    // 检查部署目录是否存在
    if (!await fs.pathExists(deployDir)) {
        console.error('❌ 部署目录不存在，请先运行 npm run package');
        process.exit(1);
    }

    console.log('✅ 部署目录存在');

    // 检查必要文件
    const requiredFiles = [
        'package.json',
        'ecosystem.config.cjs',
        'deploy.sh',
        'config.json',
        'start-frontend.js',
        'README.md',
        'server/server.js',
        'server/static-server.js',
        'server/connectionManager.js',
        'server/tcpServer.js',
        'server/tcpClient.js',
        'public/index.html'
    ];

    let allFilesExist = true;

    for (const file of requiredFiles) {
        const filePath = path.join(deployDir, file);
        if (await fs.pathExists(filePath)) {
            console.log(`✅ ${file}`);
        } else {
            console.error(`❌ ${file} 缺失`);
            allFilesExist = false;
        }
    }

    if (!allFilesExist) {
        console.error('❌ 部署包不完整');
        process.exit(1);
    }

    // 检查 package.json 内容
    const packageJson = await fs.readJson(path.join(deployDir, 'package.json'));

    const requiredDependencies = ['cors', 'express', 'ws'];
    const missingDeps = requiredDependencies.filter(dep => !packageJson.dependencies[dep]);

    if (missingDeps.length > 0) {
        console.error(`❌ 缺少依赖: ${missingDeps.join(', ')}`);
        process.exit(1);
    }

    console.log('✅ 依赖检查通过');

    // 检查前端构建文件
    const publicDir = path.join(deployDir, 'public');
    const indexHtml = path.join(publicDir, 'index.html');

    if (await fs.pathExists(indexHtml)) {
        const htmlContent = await fs.readFile(indexHtml, 'utf-8');
        if (htmlContent.includes('assets/') && htmlContent.includes('script')) {
            console.log('✅ 前端构建文件正常');
        } else {
            console.error('❌ 前端构建文件异常');
            process.exit(1);
        }
    }

    // 检查压缩包
    const tarFile = path.join(rootDir, 'network-debug-helper-deploy.tar.gz');
    if (await fs.pathExists(tarFile)) {
        const stats = await fs.stat(tarFile);
        console.log(`✅ 压缩包存在 (${(stats.size / 1024 / 1024).toFixed(2)} MB)`);
    } else {
        console.error('❌ 压缩包不存在');
        process.exit(1);
    }

    console.log('\n🎉 部署包测试通过！');
    console.log('\n📋 部署包信息:');
    console.log(`📁 部署目录: ${deployDir}`);
    console.log(`📦 压缩包: ${tarFile}`);
    console.log('\n🚀 可以开始部署了！');
}

testDeploymentPackage().catch(console.error);
