<template>
  <div class="min-h-screen bg-gray-50 p-3">
    <div class="max-w-[1200px] mx-auto bg-white rounded-lg shadow-sm p-3 ">
      <header class="flex justify-between items-center mb-3">
        <h1 class="text-lg font-medium">网络调试助手</h1>
        <span class="text-gray-500 text-sm">版本 V1.0.0</span>
      </header>

      <div class="grid grid-cols-[minmax(0,0.8fr)_minmax(0,2.2fr)] gap-3 ">
        <!-- Left Column -->
        <div class="relative" :class="{ 'w-6': isCollapsed }">
          <!-- Settings Panel -->
          <div
  class="bg-gray-50 rounded-lg shadow-sm border transition-transform duration-300 relative"
  :class="[
    isCollapsed ? 'translate-x-[-95%] shadow-none border-0' : ''
  ]"
>
            <!-- Collapse Toggle Button -->
            <button
              @click="isCollapsed = !isCollapsed"
              class="absolute top-0 z-10 w-6 h-6 bg-white rounded-full shadow-md cursor-pointer hover:bg-gray-50 flex items-center justify-center"
              :class="[isCollapsed ? 'left-0' : '-right-3']"
            >
              <el-icon :class="{'rotate-180': isCollapsed}"><ArrowLeft /></el-icon>
            </button>

            <div v-show="!isCollapsed" class="space-y-3 p-3">
              <!-- Network Settings -->
              <div class="bg-white p-3 rounded-lg">
                <h2 class="flex items-center gap-2 text-base font-medium mb-3">
                  <el-icon><Connection /></el-icon>
                  网络设置
                </h2>
                <div class="space-y-2">
                  <div>
                    <label class="block text-sm mb-1">协议类型</label>
                    <el-select v-model="networkSettings.protocol" class="w-full">
                      <el-option label="TCP Client" value="tcp-client" />
                      <el-option label="TCP Server" value="tcp-server" />
                    </el-select>
                  </div>

                  <div>
                    <label class="block text-sm mb-1">
                      {{ networkSettings.protocol === 'tcp-client' ? '远程主机地址' : '监听地址' }}
                    </label>
                    <el-select v-model="networkSettings.host" class="w-full">
                      <el-option label="0.0.0.0" value="0.0.0.0" />
                      <el-option label="127.0.0.1" value="127.0.0.1" />
                    </el-select>
                  </div>

                  <div>
                    <label class="block text-sm mb-1">
                      {{ networkSettings.protocol === 'tcp-client' ? '远程主机端口' : '监听端口' }}
                    </label>
                    <el-input v-model="networkSettings.port" placeholder="8071" />
                  </div>

                  <el-button
                    :type="isConnected ? 'danger' : 'primary'"
                    class="w-full"
                    @click="handleConnect"
                    :disabled="false"
                  >
                    {{ getConnectButtonText() }}
                    <template #loading>
                      <el-icon class="animate-spin"><Loading /></el-icon>
                    </template>
                  </el-button>
                </div>
              </div>

              <!-- Receive Settings -->
              <div class="bg-white p-3 rounded-lg">
                <h2 class="flex items-center gap-2 text-base font-medium mb-3">
                  <el-icon>
                    <Download />
                  </el-icon>
                  接收设置
                </h2>

                <div class="space-y-2">
                  <div class="flex gap-4">
                    <el-radio-group v-model="receiveSettings.format">
                      <el-radio label="ascii">ASCII</el-radio>
                      <el-radio label="hex">HEX</el-radio>
                    </el-radio-group>
                  </div>

                  <div class="space-y-1">
                    <el-checkbox v-model="receiveSettings.showTimestamp">按日志模式显示</el-checkbox>
                    <el-checkbox v-model="receiveSettings.autoWrap">接收区自动换行</el-checkbox>
                    <el-checkbox v-model="receiveSettings.saveToFile">接收数据保存文件</el-checkbox>
                  </div>
                </div>
              </div>

              <!-- Send Settings -->
              <div class="bg-white p-3 rounded-lg">
                <h2 class="flex items-center gap-2 text-base font-medium mb-3">
                  <el-icon>
                    <Upload />
                  </el-icon>
                  发送设置
                </h2>

                <div class="space-y-2">
                  <div class="flex gap-4">
                    <el-radio-group v-model="sendSettings.format">
                      <el-radio label="ascii">ASCII</el-radio>
                      <el-radio label="hex">HEX</el-radio>
                      <el-radio label="file">FILE 传输文件</el-radio>
                    </el-radio-group>
                  </div>

                  <div class="space-y-1">
                    <el-checkbox v-model="sendSettings.autoClear">自动清除发送区</el-checkbox>
                    <el-checkbox v-model="sendSettings.appendNewline">自动发送附加位</el-checkbox>
                  </div>

                  <div class="flex items-center gap-2">
                    <el-checkbox v-model="sendSettings.cycleEnabled">循环周期</el-checkbox>
                    <el-input v-model="sendSettings.cycleInterval" class="w-24" />
                    <span class="text-sm text-gray-500">ms</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Column -->
        <div
          class="space-y-3 transition-all duration-300"
          :class="[isCollapsed ? 'col-span-2' : '']" style="min-height: 775px;"
        >
          <!-- Data Panel -->
          <div class="bg-white rounded-lg shadow-sm border p-3 h-full">
            <div class="flex flex-col h-full">
              <!-- Receive Area -->
              <div class="bg-gray-50 p-3 rounded-lg flex-1">
                <div class="flex justify-between items-center mb-2">
                  <h3 class="text-sm">数据日志</h3>
                  <div class="space-x-2">
                    <el-button size="small" @click="handleClearReceive">清除接收</el-button>
                    <el-button type="primary" size="small" @click="handleSaveReceive">保存数据</el-button>
                  </div>
                </div>
                <div class="relative">
                  <!-- 美化的数据日志显示区域 -->
                  <div
                    class="data-log-container bg-white border rounded-md p-3 h-[520px] overflow-y-auto font-mono text-sm"
                    ref="dataLogContainer"
                  >
                    <div
                      v-for="(logEntry, index) in formattedLogEntries"
                      :key="index"
                      :class="getLogEntryClass(logEntry)"
                      class="log-entry mb-1 p-2 rounded border-l-4"
                    >
                      <div class="flex items-start gap-2">
                        <span class="log-icon flex-shrink-0">{{ logEntry.icon }}</span>
                        <div class="flex-1 min-w-0">
                          <div class="flex items-center gap-2 mb-1" v-if="logEntry.timestamp">
                            <span class="text-xs text-gray-500 font-normal">{{ logEntry.timestamp }}</span>
                            <span :class="getDirectionClass(logEntry.direction)" class="text-xs px-1 rounded">
                              {{ logEntry.direction }}
                            </span>
                            <span class="text-xs text-gray-600" v-if="logEntry.format">
                              [{{ logEntry.format }}]
                            </span>
                            <span class="text-xs text-gray-600" v-if="logEntry.bytes">
                              [{{ logEntry.bytes }} bytes]
                            </span>
                            <span class="text-xs text-gray-600" v-if="logEntry.client">
                              {{ logEntry.client }}
                            </span>
                          </div>
                          <div class="log-content break-all" :class="getContentClass(logEntry.type)">
                            {{ logEntry.content }}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-if="formattedLogEntries.length === 0" class="text-gray-400 text-center py-8">
                      暂无数据日志
                    </div>
                  </div>
                  <el-button
                    class="!absolute top-2 right-2 !p-2"
                    size="small"
                    @click="receiveDialogVisible = true"
                  >
                    <el-icon><FullScreen /></el-icon>
                  </el-button>
                </div>
              </div>

              <!-- Send Area -->
              <div class="bg-gray-50 p-3 rounded-lg mt-3">
                <div class="flex justify-between items-center mb-2">
                  <h3 class="text-sm">数据发送区</h3>
                  <div class="space-x-2">
                    <el-button size="small" @click="handleClearSend">清除发送</el-button>
                    <el-button
                      type="primary"
                      size="small"
                      @click="handleSend"
                      :disabled="!sendData.trim()"
                    >发送数据</el-button>
                  </div>
                </div>
                <div class="relative">
                  <el-input
                    type="textarea"
                    v-model="sendData"
                    :rows="3"
                    class="font-mono"
                  />
                  <el-button
                    class="!absolute bottom-2 right-2 !p-2"
                    size="small"
                    @click="sendDialogVisible = true"
                  >
                    <el-icon><FullScreen /></el-icon>
                  </el-button>
                </div>

                <!-- Action Buttons -->
                <div class="flex gap-2 mt-2">
                  <el-button>历史发送</el-button>
                  <el-button>批量发送</el-button>
                  <el-button @click="autoReplyDialogVisible = true">自动应答</el-button>
                  <el-button>进制转换</el-button>
                  <el-button>CRC/BCC/LRC</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <footer class="mt-3 flex justify-between items-center text-sm text-gray-500">
        <div>
          <span>数据统计 RX: {{ stats.rx }}</span>
          <span class="ml-4">TX: {{ stats.tx }}</span>
        </div>
        <div>
          <el-button
            link
            type="primary"
            @click="resetStats"
          >复位计数</el-button>
        </div>
      </footer>
    </div>

    <!-- 数据日志弹窗 -->
    <el-dialog
      v-model="receiveDialogVisible"
      title="数据日志"
      width="80%"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-input
        type="textarea"
        v-model="receiveData"
        :rows="25"
        class="font-mono"
        :wrap="receiveSettings.autoWrap ? 'soft' : 'off'"
        readonly
      />
    </el-dialog>

    <!-- 数据发送区弹窗 -->
    <el-dialog
      v-model="sendDialogVisible"
      title="数据发送区"
      width="80%"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-input
        type="textarea"
        v-model="sendData"
        :rows="25"
        class="font-mono"
      />
      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button @click="sendDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleSend"
            :disabled="!sendData.trim()"
          >发送</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 自动应答管理对话框 -->
    <el-dialog
      v-model="autoReplyDialogVisible"
      title="自动应答管理"
      width="90%"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <div class="space-y-4">
        <!-- 规则列表 -->
        <div class="bg-gray-50 p-4 rounded-lg">
          <div class="flex justify-between items-center mb-3">
            <h3 class="text-lg font-medium">自动应答规则</h3>
            <el-button type="primary" @click="addAutoReplyRule">
              <el-icon><Plus /></el-icon>
              添加规则
            </el-button>
          </div>

          <div v-if="autoReplyRules.length === 0" class="text-center text-gray-500 py-8">
            暂无自动应答规则，点击"添加规则"创建第一个规则
          </div>

          <div v-else class="space-y-2">
            <div
              v-for="rule in autoReplyRules"
              :key="rule.id"
              class="bg-white p-3 rounded border"
              :class="{ 'opacity-50': !rule.enabled }"
            >
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <div class="flex items-center gap-3 mb-2">
                    <el-switch
                      v-model="rule.enabled"
                      @change="toggleAutoReplyRule(rule.id)"
                      :active-text="rule.enabled ? '启用' : '停用'"
                    />
                    <span class="font-medium">{{ rule.name }}</span>
                    <el-tag :type="rule.enabled ? 'success' : 'info'" size="small">
                      {{ rule.enabled ? '启用' : '停用' }}
                    </el-tag>
                  </div>
                  <div class="text-sm text-gray-600 space-y-1">
                    <div>
                      <span class="font-medium">匹配内容:</span>
                      <el-tag size="small" class="ml-1">{{ rule.matchFormat.toUpperCase() }}</el-tag>
                      <span class="ml-1 font-mono">{{ rule.matchContent }}</span>
                    </div>
                    <div>
                      <span class="font-medium">应答内容:</span>
                      <el-tag size="small" class="ml-1">{{ rule.replyFormat.toUpperCase() }}</el-tag>
                      <span class="ml-1 font-mono">{{ rule.replyContent }}</span>
                    </div>
                    <div>
                      <span class="font-medium">延迟:</span>
                      <span class="ml-1">{{ rule.delayMs }}ms</span>
                    </div>
                  </div>
                </div>
                <div class="flex gap-2">
                  <el-button size="small" @click="editAutoReplyRule(rule)">编辑</el-button>
                  <el-button size="small" type="danger" @click="deleteAutoReplyRule(rule.id)">删除</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 自动应答规则编辑对话框 -->
    <el-dialog
      v-model="autoReplyEditDialogVisible"
      :title="isEditingAutoReply ? '编辑自动应答规则' : '添加自动应答规则'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="currentAutoReplyRule" label-width="100px" class="space-y-4">
        <el-form-item label="规则名称" required>
          <el-input v-model="currentAutoReplyRule.name" placeholder="请输入规则名称" />
        </el-form-item>

        <el-form-item label="匹配内容" required>
          <div class="space-y-2">
            <el-radio-group v-model="currentAutoReplyRule.matchFormat">
              <el-radio label="hex">HEX</el-radio>
              <el-radio label="ascii">ASCII</el-radio>
            </el-radio-group>
            <el-input
              v-model="currentAutoReplyRule.matchContent"
              type="textarea"
              :rows="3"
              :placeholder="currentAutoReplyRule.matchFormat === 'hex' ? '请输入十六进制数据，如: 01 02 03' : '请输入ASCII文本'"
              class="font-mono"
            />
          </div>
        </el-form-item>

        <el-form-item label="应答内容" required>
          <div class="space-y-2">
            <el-radio-group v-model="currentAutoReplyRule.replyFormat">
              <el-radio label="hex">HEX</el-radio>
              <el-radio label="ascii">ASCII</el-radio>
            </el-radio-group>
            <el-input
              v-model="currentAutoReplyRule.replyContent"
              type="textarea"
              :rows="3"
              :placeholder="currentAutoReplyRule.replyFormat === 'hex' ? '请输入十六进制数据，如: 01 02 03' : '请输入ASCII文本'"
              class="font-mono"
            />
          </div>
        </el-form-item>

        <el-form-item label="应答延迟">
          <div class="flex items-center gap-2">
            <el-input-number
              v-model="currentAutoReplyRule.delayMs"
              :min="0"
              :max="10000"
              :step="10"
              class="w-32"
            />
            <span class="text-sm text-gray-500">毫秒</span>
          </div>
        </el-form-item>

        <el-form-item label="启用状态">
          <el-switch v-model="currentAutoReplyRule.enabled" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button @click="autoReplyEditDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveAutoReplyRule">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
  </template>

<script setup lang="ts">
import { ref, nextTick, watch, computed } from 'vue'
import './assets/styles.css';  // 引入 styles.css
import { Connection, Download, Upload, ArrowLeft, FullScreen, Loading, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const isCollapsed = ref(false)

  const networkSettings = ref({
  protocol: 'tcp-server',
    host: '0.0.0.0',
  port: '8899'
})

const receiveSettings = ref({
  format: 'hex',
  showTimestamp: true,
  autoWrap: true,
  saveToFile: false
})

const sendSettings = ref({
  format: 'hex',
  autoClear: false,
  appendNewline: false,
  cycleEnabled: false,
  cycleInterval: '1000'
})

const receiveData = ref('')

const sendData = ref('')

const receiveDialogVisible = ref(false)
const sendDialogVisible = ref(false)

// 添加连接状态
const isConnected = ref(false)

// 添加 WebSocket 实例
let ws: WebSocket | null = null;

// 添加数据统计计数器
const stats = ref({
  rx: 0,
  tx: 0
})

// 日志条目数组，用于美化显示
const logEntries = ref([])

// 数据日志容器引用
const dataLogContainer = ref<HTMLElement | null>(null)

// 自动应答规则
const autoReplyRules = ref([])

// 自动应答对话框显示状态
const autoReplyDialogVisible = ref(false)

// 自动应答规则编辑对话框显示状态
const autoReplyEditDialogVisible = ref(false)

// 当前编辑的自动应答规则
const currentAutoReplyRule = ref({
  id: '',
  name: '',
  enabled: true,
  matchContent: '',
  matchFormat: 'hex',
  replyContent: '',
  replyFormat: 'hex',
  delayMs: 100
})

// 是否为编辑模式
const isEditingAutoReply = ref(false)

// 添加接收数据格式转换函数
const formatReceiveData = (hexData: string, rawData: string, format: string): string => {
  if (format === 'hex') {
    // HEX 格式显示：直接使用后端发送的十六进制数据
    return hexData;
  } else {
    // ASCII 格式显示：使用原始数据，将不可打印字符显示为点号
    if (rawData) {
      return rawData.replace(/[\x00-\x1F\x7F-\x9F]/g, '.');
    }
    // 如果没有原始数据，从十六进制转换
    if (/^[0-9A-Fa-f\s]+$/.test(hexData)) {
      const hexArray = hexData.replace(/\s+/g, '').match(/.{1,2}/g) || [];
      return hexArray
        .map((hex: string) => {
          const charCode = parseInt(hex, 16);
          // 对于不可打印字符，显示为点号
          return (charCode >= 32 && charCode <= 126) ? String.fromCharCode(charCode) : '.';
        })
        .join('');
    }
    return hexData;
  }
};

// 添加发送数据格式转换函数
const formatSendData = (data: string, format: string): string => {
  if (format === 'hex') {
    // HEX 格式：如果不是有效的十六进制，将 ASCII 转换为 HEX
    if (!/^[0-9A-Fa-f\s]*$/.test(data.replace(/\s+/g, ''))) {
      return Array.from(data)
        .map(char => char.charCodeAt(0).toString(16).padStart(2, '0').toUpperCase())
        .join(' ');
    }
    return data;
  } else {
    // ASCII 格式：如果是十六进制格式，转换为 ASCII
    if (/^[0-9A-Fa-f\s]+$/.test(data)) {
      const hexArray = data.replace(/\s+/g, '').match(/.{1,2}/g) || [];
      return hexArray
        .map((hex: string) => String.fromCharCode(parseInt(hex, 16)))
        .join('');
    }
    return data;
  }
};

// 添加时间格式化函数
const formatDateTime = (isoString: string) => {
  const date = new Date(isoString);
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}.${date.getMilliseconds().toString().padStart(3, '0')}`;
};

// 添加重置计数器的函数
const resetStats = () => {
  stats.value.rx = 0;
  stats.value.tx = 0;
}

// 获取API基础URL
const getApiBaseUrl = () => {
  // 在生产环境中，使用当前页面的host，但端口改为3000
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    return 'http://localhost:3000';
  } else {
    return `http://${window.location.hostname}:3000`;
  }
};

// 获取WebSocket URL
const getWebSocketUrl = () => {
  // 在生产环境中，使用当前页面的host，但端口改为3000
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    return 'ws://localhost:3000/ws';
  } else {
    return `ws://${window.location.hostname}:3000/ws`;
  }
};

// 修改处理连接的函数
const handleConnect = async () => {
  try {
    if (isConnected.value) {
      // 如果已经连接，则断开连接
      const response = await fetch(`${getApiBaseUrl()}/api/disconnect`, {
        method: 'POST'
      });

      if (response.ok) {
        isConnected.value = false;
        ws?.close();
        ws = null;
        ElMessage({
          message: networkSettings.value.protocol === 'tcp-server' ? '已停止监听' : '已断开连接',
          type: 'success',
          duration: 2000
        });
      }
      return;
    }

    const config = {
      protocol: networkSettings.value.protocol,
      host: networkSettings.value.host,
      port: networkSettings.value.port,
      receiveSettings: {
        format: receiveSettings.value.format,
        showTimestamp: receiveSettings.value.showTimestamp,
        autoWrap: receiveSettings.value.autoWrap,
        saveToFile: receiveSettings.value.saveToFile
      }
    };

    // 先建立 WebSocket 连接
    ws = new WebSocket(getWebSocketUrl());

    ws.onopen = async () => {
      try {
        // WebSocket 连接成功后，发送连接请求
        const response = await fetch(`${getApiBaseUrl()}/api/connect`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(config)
        });

        const data = await response.json();

        if (data.success) {
          isConnected.value = true;
          ElMessage({
            message: networkSettings.value.protocol === 'tcp-client' ? '连接成功' : '监听成功',
            type: 'success',
            duration: 2000
          });
        } else {
          throw new Error(data.error || '连接失败');
        }
      } catch (error) {
        ws?.close();
        ws = null;
        throw error;
      }
    };

    ws.onmessage = (event) => {
      const receivedData = JSON.parse(event.data);

      if (receivedData.type === 'error') {
        ElMessage({
          message: receivedData.message,
          type: 'error',
          duration: 2000
        });
        return;
      }

      if (receivedData.type === 'connection') {
        // 处理连接状态消息
        handleConnectionStatus(receivedData);
        return;
      }

      // 更新接收字节计数
      if (receivedData.direction === 'receive') {
        stats.value.rx += receivedData.data.length;

        // 检查自动应答规则
        checkAutoReply(receivedData.data);
      }

      // 根据当前设置格式化数据
      let formattedData = formatReceiveData(receivedData.data, receivedData.rawData || '', receiveSettings.value.format);

      // 添加时间戳等信息
      if (receiveSettings.value.showTimestamp) {
        formattedData = `${formatDateTime(receivedData.timestamp)} ${receivedData.direction === 'receive' ? '↓' : '↑'} [${receiveSettings.value.format.toUpperCase()}] [${receivedData.data.length} bytes] ${formattedData}`;
      }

      // 添加到接收区
      if (receiveData.value) {
        // 如果启用了自动换行，添加换行符
        receiveData.value += (receiveSettings.value.autoWrap ? '\n' : '') + formattedData;
      } else {
        receiveData.value = formattedData;
      }

      // 滚动到底部
      nextTick(() => {
        if (dataLogContainer.value) {
          dataLogContainer.value.scrollTop = dataLogContainer.value.scrollHeight;
        }
      });
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      ElMessage({
        message: networkSettings.value.protocol === 'tcp-server' ?
          'WebSocket监听错误' : 'WebSocket连接错误',
        type: 'error',
        duration: 2000
      });
      isConnected.value = false;
    };

    ws.onclose = () => {
      isConnected.value = false;
      ws = null;
      ElMessage({
        message: 'WebSocket连接已关闭',
        type: 'warning',
        duration: 2000
      });
    };

  } catch (error) {
    console.error('连接错误:', error);
    isConnected.value = false;
    ws?.close();
    ws = null;
    ElMessage({
      message: error.message || (networkSettings.value.protocol === 'tcp-client' ? '连接失败' : '监听失败'),
      type: 'error',
      duration: 2000
    });
  }
};

// 修改发送数据的处理函数
const handleSend = async () => {
  if (!isConnected.value) {
    ElMessage({
      message: networkSettings.value.protocol === 'tcp-server' ?
        '请先开始监听' : '请先连接到服务器',
      type: 'warning',
      duration: 2000
    });
    return;
  }

  if (!sendData.value.trim()) {
    ElMessage({
      message: '发送内容不能为空',
      type: 'warning',
      duration: 2000
    });
    return;
  }

  // 如果是 HEX 格式，验证输入
  if (sendSettings.value.format === 'hex') {
    const hexString = sendData.value.replace(/\s+/g, '');
    if (!/^[0-9A-Fa-f]*$/.test(hexString)) {
      ElMessage({
        message: 'HEX 格式无效，只能包含 0-9 和 A-F 字符',
        type: 'error',
        duration: 2000
      });
      return;
    }
    if (hexString.length % 2 !== 0) {
      ElMessage({
        message: 'HEX 格式无效，字符数必须是偶数',
        type: 'error',
        duration: 2000
      });
      return;
    }
  }

  try {
    const response = await fetch(`${getApiBaseUrl()}/api/send`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        content: sendData.value,
        format: sendSettings.value.format,
        appendNewline: sendSettings.value.appendNewline
      })
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || '发送失败');
    }

    // 更新发送字节计数
    stats.value.tx += sendData.value.length;

    // 发送成功后，将发送的内容添加到数据日志
    const now = new Date();
    const timestamp = now.getFullYear() + '-' +
      (now.getMonth() + 1).toString().padStart(2, '0') + '-' +
      now.getDate().toString().padStart(2, '0') + ' ' +
      now.getHours().toString().padStart(2, '0') + ':' +
      now.getMinutes().toString().padStart(2, '0') + ':' +
      now.getSeconds().toString().padStart(2, '0') + '.' +
      now.getMilliseconds().toString().padStart(3, '0');

    // 格式化发送的数据（用于显示在日志中）
    let formattedData = formatSendData(sendData.value, sendSettings.value.format);

    // 添加到数据日志
    const logEntry = receiveSettings.value.showTimestamp
      ? `${timestamp} ↑ [${sendSettings.value.format.toUpperCase()}] [${sendData.value.length} bytes] ${formattedData}`
      : formattedData;

    receiveData.value += (receiveData.value ? '\n' : '') + logEntry;

    // 如果设置了自动清除，则清空发送区
    if (sendSettings.value.autoClear) {
      sendData.value = '';
    }

    // 滚动到底部
    nextTick(() => {
      if (dataLogContainer.value) {
        dataLogContainer.value.scrollTop = dataLogContainer.value.scrollHeight;
      }
    });

  } catch (error) {
    console.error('发送错误:', error);
    ElMessage({
      message: '发送失败',
      type: 'error',
      duration: 2000
    });
  }
};

// 添加清除接收区的处理函数
const handleClearReceive = () => {
  receiveData.value = '';
  logEntries.value = [];
}

// 添加保存数据的处理函数
const handleSaveReceive = () => {
  // 创建 Blob 对象
  const blob = new Blob([receiveData.value], { type: 'text/plain;charset=utf-8' });

  // 创建下载链接
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);

  // 设置文件名（使用当前时间戳）
  const date = new Date();
  const fileName = `network-debug-${date.getFullYear()}${(date.getMonth()+1).toString().padStart(2,'0')}${date.getDate().toString().padStart(2,'0')}_${date.getHours().toString().padStart(2,'0')}${date.getMinutes().toString().padStart(2,'0')}${date.getSeconds().toString().padStart(2,'0')}.txt`;
  link.download = fileName;

  // 触发下载
  document.body.appendChild(link);
  link.click();

  // 清理
  document.body.removeChild(link);
  URL.revokeObjectURL(link.href);
};

// 添加清除发送区的处理函数
const handleClearSend = () => {
  sendData.value = '';
};

// 添加格式切换的监听
watch(() => receiveSettings.value.format, (newFormat) => {
  ElMessage({
    message: `已切换到 ${newFormat.toUpperCase()} 格式，后续接收的数据将以 ${newFormat.toUpperCase()} 格式显示`,
    type: 'success',
    duration: 2000
  });
});

// 添加处理连接状态的函数
const handleConnectionStatus = (statusData: any) => {
  const { status, connectionInfo, timestamp } = statusData;
  let message = '';

  if (status === 'connected') {
    if (connectionInfo.type === 'server') {
      message = `🟢 TCP 服务器已启动，监听 ${connectionInfo.host}:${connectionInfo.port}`;
    } else if (connectionInfo.type === 'client') {
      message = `🟢 TCP 客户端已连接到 ${connectionInfo.host}:${connectionInfo.port}`;
    } else if (connectionInfo.type === 'client_to_server') {
      message = `🟢 新客户端连接: ${connectionInfo.clientInfo}`;
    }
  } else if (status === 'disconnected') {
    if (connectionInfo.type === 'server') {
      message = `🔴 TCP 服务器已停止监听 ${connectionInfo.host}:${connectionInfo.port}`;
    } else if (connectionInfo.type === 'client') {
      message = `🔴 TCP 客户端已断开连接 ${connectionInfo.host}:${connectionInfo.port}`;
    } else if (connectionInfo.type === 'client_to_server') {
      message = `🔴 客户端断开连接: ${connectionInfo.clientInfo}`;
    }
  }

  if (message) {
    const logEntry = receiveSettings.value.showTimestamp
      ? `${formatDateTime(timestamp)} ℹ️ [系统] ${message}`
      : message;

    // 添加到接收区
    if (receiveData.value) {
      receiveData.value += (receiveSettings.value.autoWrap ? '\n' : '') + logEntry;
    } else {
      receiveData.value = logEntry;
    }

    // 滚动到底部
    nextTick(() => {
      if (dataLogContainer.value) {
        dataLogContainer.value.scrollTop = dataLogContainer.value.scrollHeight;
      }
    });
  }
};

// 解析日志条目的函数
const parseLogEntry = (logText: string) => {
  // 解析系统消息
  if (logText.includes('ℹ️ [系统]')) {
    const timestampMatch = logText.match(/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})/);
    const timestamp = timestampMatch ? timestampMatch[1] : '';
    const content = logText.replace(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}\s*ℹ️\s*\[系统\]\s*/, '');

    return {
      type: 'system',
      timestamp: timestamp,
      content: content,
      icon: 'ℹ️',
      direction: '系统',
      format: '',
      bytes: '',
      client: ''
    };
  }

  // 解析数据消息
  const dataMatch = logText.match(/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\s*(↓|↑)\s*\[([^\]]+)\]\s*\[([^\]]+)\]\s*(.*)$/);
  if (dataMatch) {
    return {
      type: dataMatch[2] === '↓' ? 'receive' : 'send',
      timestamp: dataMatch[1],
      direction: dataMatch[2] === '↓' ? '接收' : '发送',
      format: dataMatch[3],
      bytes: dataMatch[4],
      content: dataMatch[5],
      icon: dataMatch[2] === '↓' ? '📥' : '📤',
      client: ''
    };
  }

  // 默认处理
  return {
    type: 'data',
    timestamp: '',
    direction: '',
    format: '',
    bytes: '',
    content: logText,
    icon: '📄',
    client: ''
  };
};

// 计算属性：格式化的日志条目
const formattedLogEntries = computed(() => {
  if (!receiveData.value) return [];

  return receiveData.value.split('\n')
    .filter(line => line.trim())
    .map(line => parseLogEntry(line));
});

// 获取日志条目样式类
const getLogEntryClass = (logEntry: any) => {
  const baseClass = 'transition-all duration-200 hover:shadow-sm';

  switch (logEntry.type) {
    case 'system':
      if (logEntry.content.includes('🟢')) {
        return `${baseClass} bg-green-50 border-green-200 hover:bg-green-100`;
      } else if (logEntry.content.includes('🔴')) {
        return `${baseClass} bg-red-50 border-red-200 hover:bg-red-100`;
      }
      return `${baseClass} bg-blue-50 border-blue-200 hover:bg-blue-100`;
    case 'receive':
      return `${baseClass} bg-indigo-50 border-indigo-200 hover:bg-indigo-100`;
    case 'send':
      return `${baseClass} bg-emerald-50 border-emerald-200 hover:bg-emerald-100`;
    default:
      return `${baseClass} bg-gray-50 border-gray-200 hover:bg-gray-100`;
  }
};

// 获取方向标签样式类
const getDirectionClass = (direction: string) => {
  switch (direction) {
    case '接收':
      return 'bg-indigo-100 text-indigo-700';
    case '发送':
      return 'bg-emerald-100 text-emerald-700';
    case '系统':
      return 'bg-blue-100 text-blue-700';
    default:
      return 'bg-gray-100 text-gray-700';
  }
};

// 获取内容样式类
const getContentClass = (type: string) => {
  switch (type) {
    case 'system':
      return 'text-gray-700 font-medium';
    case 'receive':
      return 'text-indigo-800 font-mono';
    case 'send':
      return 'text-emerald-800 font-mono';
    default:
      return 'text-gray-800 font-mono';
  }
};

// 添加获取按钮文本的函数
const getConnectButtonText = () => {
  if (networkSettings.value.protocol === 'tcp-server') {
    return isConnected.value ? '停止监听' : '开始监听';
  }
  return isConnected.value ? '断开连接' : '开始连接';
};

// 自动应答相关函数
const generateRuleId = () => {
  return 'rule_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
};

// 添加新的自动应答规则
const addAutoReplyRule = () => {
  currentAutoReplyRule.value = {
    id: '',
    name: '',
    enabled: true,
    matchContent: '',
    matchFormat: 'hex',
    replyContent: '',
    replyFormat: 'hex',
    delayMs: 100
  };
  isEditingAutoReply.value = false;
  autoReplyEditDialogVisible.value = true;
};

// 编辑自动应答规则
const editAutoReplyRule = (rule: any) => {
  currentAutoReplyRule.value = { ...rule };
  isEditingAutoReply.value = true;
  autoReplyEditDialogVisible.value = true;
};

// 保存自动应答规则
const saveAutoReplyRule = () => {
  // 验证输入
  if (!currentAutoReplyRule.value.name.trim()) {
    ElMessage({
      message: '请输入规则名称',
      type: 'warning',
      duration: 2000
    });
    return;
  }

  if (!currentAutoReplyRule.value.matchContent.trim()) {
    ElMessage({
      message: '请输入匹配内容',
      type: 'warning',
      duration: 2000
    });
    return;
  }

  if (!currentAutoReplyRule.value.replyContent.trim()) {
    ElMessage({
      message: '请输入应答内容',
      type: 'warning',
      duration: 2000
    });
    return;
  }

  // 验证HEX格式
  if (currentAutoReplyRule.value.matchFormat === 'hex') {
    const hexString = currentAutoReplyRule.value.matchContent.replace(/\s+/g, '');
    if (!/^[0-9A-Fa-f]*$/.test(hexString)) {
      ElMessage({
        message: '匹配内容HEX格式无效，只能包含0-9和A-F字符',
        type: 'error',
        duration: 2000
      });
      return;
    }
  }

  if (currentAutoReplyRule.value.replyFormat === 'hex') {
    const hexString = currentAutoReplyRule.value.replyContent.replace(/\s+/g, '');
    if (!/^[0-9A-Fa-f]*$/.test(hexString)) {
      ElMessage({
        message: '应答内容HEX格式无效，只能包含0-9和A-F字符',
        type: 'error',
        duration: 2000
      });
      return;
    }
  }

  if (isEditingAutoReply.value) {
    // 编辑现有规则
    const index = autoReplyRules.value.findIndex(rule => rule.id === currentAutoReplyRule.value.id);
    if (index !== -1) {
      autoReplyRules.value[index] = { ...currentAutoReplyRule.value };
    }
  } else {
    // 添加新规则
    currentAutoReplyRule.value.id = generateRuleId();
    autoReplyRules.value.push({ ...currentAutoReplyRule.value });
  }

  autoReplyEditDialogVisible.value = false;
  ElMessage({
    message: isEditingAutoReply.value ? '规则更新成功' : '规则添加成功',
    type: 'success',
    duration: 2000
  });
};

// 删除自动应答规则
const deleteAutoReplyRule = (ruleId) => {
  const index = autoReplyRules.value.findIndex(rule => rule.id === ruleId);
  if (index !== -1) {
    autoReplyRules.value.splice(index, 1);
    ElMessage({
      message: '规则删除成功',
      type: 'success',
      duration: 2000
    });
  }
};

// 切换规则启用状态
const toggleAutoReplyRule = (ruleId) => {
  const rule = autoReplyRules.value.find(rule => rule.id === ruleId);
  if (rule) {
    rule.enabled = !rule.enabled;
    ElMessage({
      message: rule.enabled ? '规则已启用' : '规则已停用',
      type: 'success',
      duration: 2000
    });
  }
};

// 检查是否匹配自动应答规则
const checkAutoReply = async (receivedData) => {
  for (const rule of autoReplyRules.value) {
    if (!rule.enabled) continue;

    let matchContent = rule.matchContent;
    let receivedContent = receivedData;

    // 格式转换以便比较
    if (rule.matchFormat === 'hex') {
      // 如果规则是HEX格式，将接收到的数据转换为HEX进行比较
      if (typeof receivedData === 'string') {
        receivedContent = Array.from(receivedData)
          .map(char => char.charCodeAt(0).toString(16).padStart(2, '0').toUpperCase())
          .join(' ');
      }
      matchContent = rule.matchContent.replace(/\s+/g, ' ').toUpperCase();
      receivedContent = receivedContent.replace(/\s+/g, ' ').toUpperCase();
    } else {
      // 如果规则是ASCII格式，确保都是字符串格式
      if (typeof receivedData !== 'string') {
        // 如果接收到的是HEX，转换为ASCII
        const hexArray = receivedData.replace(/\s+/g, '').match(/.{1,2}/g) || [];
        receivedContent = hexArray
          .map(hex => String.fromCharCode(parseInt(hex, 16)))
          .join('');
      }
    }

    // 检查是否匹配
    if (receivedContent.includes(matchContent)) {
      // 延迟后发送应答
      setTimeout(async () => {
        try {
          const response = await fetch(`${getApiBaseUrl()}/api/send`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              content: rule.replyContent,
              format: rule.replyFormat,
              appendNewline: false
            })
          });

          const result = await response.json();
          if (result.success) {
            // 在日志中显示自动应答信息
            const now = new Date();
            const timestamp = now.getFullYear() + '-' +
              (now.getMonth() + 1).toString().padStart(2, '0') + '-' +
              now.getDate().toString().padStart(2, '0') + ' ' +
              now.getHours().toString().padStart(2, '0') + ':' +
              now.getMinutes().toString().padStart(2, '0') + ':' +
              now.getSeconds().toString().padStart(2, '0') + '.' +
              now.getMilliseconds().toString().padStart(3, '0');

            const logEntry = receiveSettings.value.showTimestamp
              ? `${timestamp} 🤖 [自动应答] 规则"${rule.name}"触发，应答: ${rule.replyContent}`
              : `🤖 [自动应答] 规则"${rule.name}"触发，应答: ${rule.replyContent}`;

            receiveData.value += (receiveData.value ? '\n' : '') + logEntry;

            // 滚动到底部
            nextTick(() => {
              if (dataLogContainer.value) {
                dataLogContainer.value.scrollTop = dataLogContainer.value.scrollHeight;
              }
            });
          }
        } catch (error) {
          console.error('自动应答发送失败:', error);
        }
      }, rule.delayMs);

      break; // 只触发第一个匹配的规则
    }
  }
};
  </script>

<style>
/* @tailwind base;
@tailwind components;
@tailwind utilities; */

/* 数据日志容器样式 */
.data-log-container {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.data-log-container::-webkit-scrollbar {
  width: 6px;
}

.data-log-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.data-log-container::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.data-log-container::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 日志条目样式 */
.log-entry {
  transition: all 0.2s ease-in-out;
}

.log-entry:hover {
  transform: translateX(2px);
}

/* 日志内容样式 */
.log-content {
  line-height: 1.4;
  word-break: break-all;
}

/* 系统消息特殊样式 */
.log-entry.system .log-content {
  font-weight: 500;
}

/* 数据消息等宽字体 */
.log-entry.data .log-content,
.log-entry.receive .log-content,
.log-entry.send .log-content {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
}

/* 时间戳样式 */
.log-entry .text-xs {
  font-size: 11px;
}

/* 图标样式 */
.log-icon {
  font-size: 16px;
  line-height: 1;
  margin-top: 2px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .data-log-container {
    height: 400px !important;
  }

  .log-entry {
    padding: 8px;
    margin-bottom: 4px;
  }

  .log-content {
    font-size: 12px;
  }
}
</style>