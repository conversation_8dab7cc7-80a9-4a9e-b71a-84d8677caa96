#!/usr/bin/env node

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 设置工作目录为项目根目录
const projectRoot = __dirname;
const staticServerPath = path.join(__dirname, 'server', 'static-server.js');

console.log(`项目根目录: ${projectRoot}`);
console.log(`静态服务器路径: ${staticServerPath}`);

// 启动静态服务器，工作目录设置为项目根目录
const child = spawn('node', [staticServerPath], {
    cwd: projectRoot,
    stdio: 'inherit',
    env: {
        ...process.env,
        PORT: process.env.PORT || 5000,
        HOST: process.env.HOST || '0.0.0.0',
        STATIC_DIR: process.env.STATIC_DIR || './public'
    }
});

child.on('error', (error) => {
    console.error('启动前端服务器失败:', error);
    process.exit(1);
});

child.on('exit', (code) => {
    console.log(`前端服务器退出，代码: ${code}`);
    process.exit(code);
});
